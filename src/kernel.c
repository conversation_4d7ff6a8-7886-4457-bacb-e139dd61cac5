/* Minimal freestanding kernel in C printing "42" to VGA text memory */

/* Compiler flags defined in Makefile must include -m32 and -ffreestanding */

typedef unsigned int   u32;
typedef unsigned short u16;
typedef unsigned char  u8;
typedef unsigned long  size_t;

/* VGA text mode constants */
#define VGA_WIDTH  80
#define VGA_HEIGHT 25
#define VGA_MEMORY 0xB8000

/* VGA color constants */
enum vga_color {
    VGA_COLOR_BLACK = 0,
    VGA_COLOR_BLUE = 1,
    VGA_COLOR_GREEN = 2,
    VGA_COLOR_CYAN = 3,
    VGA_COLOR_RED = 4,
    VGA_COLOR_MAGENTA = 5,
    VGA_COLOR_BROWN = 6,
    VGA_COLOR_LIGHT_GREY = 7,
    VGA_COLOR_DARK_GREY = 8,
    VGA_COLOR_LIGHT_BLUE = 9,
    VGA_COLOR_LIGHT_GREEN = 10,
    VGA_COLOR_LIGHT_CYAN = 11,
    VGA_COLOR_LIGHT_RED = 12,
    VGA_COLOR_LIGHT_MAGENTA = 13,
    VGA_COLOR_LIGHT_BROWN = 14,
    VGA_COLOR_WHITE = 15,
};

/* Export symbol for assembly to call */
void kernel_main(void);

static inline u8 vga_entry_color(enum vga_color fg, enum vga_color bg) {
    return fg | bg << 4;
}

static inline u16 vga_entry(unsigned char uc, u8 color) {
    return (u16) uc | (u16) color << 8;
}

static inline void write_char(int x, int y, char c, u8 color) {
    if (x >= 0 && x < VGA_WIDTH && y >= 0 && y < VGA_HEIGHT) {
        volatile u16* vga = (volatile u16*)VGA_MEMORY;
        const size_t index = y * VGA_WIDTH + x;
        vga[index] = vga_entry(c, color);
    }
}

static void clear_screen(void) {
    const u8 color = vga_entry_color(VGA_COLOR_LIGHT_GREY, VGA_COLOR_BLACK);
    volatile u16* vga = (volatile u16*)VGA_MEMORY;

    for (size_t i = 0; i < VGA_WIDTH * VGA_HEIGHT; i++) {
        vga[i] = vga_entry(' ', color);
    }
}

void itoa_decimal(int value, char *buf) {
    char tmp[12];
    int i = 0;
    if (value == 0) { buf[0] = '0'; buf[1] = '\0'; return; }
    int v = value;
    if (v < 0) { buf[0] = '-'; v = -v; }
    while (v) {
        tmp[i++] = '0' + (v % 10);
        v /= 10;
    }
    int j = 0;
    if (buf[0] == '-') j = 1;
    while (i) buf[j++] = tmp[--i];
    buf[j] = '\0';
}

void kernel_main(void) {
    /* Clear the screen first */
    clear_screen();

    /* Set up colors */
    const u8 color = vga_entry_color(VGA_COLOR_WHITE, VGA_COLOR_BLACK);

    /* Write "42" at top-left */
    char s[12];
    itoa_decimal(42, s);
    int i = 0;
    while (s[i]) {
        write_char(i, 0, s[i], color);
        i++;
    }

    /* Write a success message */
    const char* msg = " - KFS-1 Kernel Loaded Successfully!";
    i = 0;
    while (msg[i]) {
        write_char(i + 2, 0, msg[i], color);
        i++;
    }

    /* Infinite loop with halt instruction */
    for(;;) {
        __asm__ volatile ("hlt");
    }
}
