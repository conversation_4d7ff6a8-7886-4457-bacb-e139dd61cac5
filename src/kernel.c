/* Minimal freestanding kernel in C printing "42" to VGA text memory */

/* Compiler flags defined in Makefile must include -m32 and -ffreestanding */

typedef unsigned int   u32;
typedef unsigned short u16;
typedef unsigned char  u8;

/* Export symbol for assembly to call */
void kernel_main(void);

static inline void write_char(int x, int y, char c, u8 attr) {
    volatile u16* vga = (volatile u16*)0xB8000;
    u16 index = y * 80 + x;
    vga[index] = (u16)c | ((u16)attr << 8);
}

void itoa_decimal(int value, char *buf) {
    char tmp[12];
    int i = 0;
    if (value == 0) { buf[0] = '0'; buf[1] = '\0'; return; }
    int v = value;
    if (v < 0) { buf[0] = '-'; v = -v; }
    while (v) {
        tmp[i++] = '0' + (v % 10);
        v /= 10;
    }
    int j = 0;
    if (buf[0] == '-') j = 1;
    while (i) buf[j++] = tmp[--i];
    buf[j] = '\0';
}

void kernel_main(void) {
    /* write "42" at top-left with white-on-black */
    char s[12];
    itoa_decimal(42, s);
    int i = 0;
    while (s[i]) {
        write_char(i, 0, s[i], 0x07);
        i++;
    }

    /* optional: infinite loop */
    for(;;) { __asm__ volatile ("hlt"); }
}
