; NASM 32-bit multiboot header + start
BITS 32

section .multiboot
align 4
; Multiboot header with address specification
MULTIBOOT_MAGIC     equ 0x1BADB002
MULTIBOOT_FLAGS     equ 0x00010000    ; flag bit 16: addresses in header
MULTIBOOT_CHECKSUM  equ -(MULTIBOOT_MAGIC + MULTIBOOT_FLAGS)

dd MULTIBOOT_MAGIC      ; magic
dd MULTIBOOT_FLAGS      ; flags
dd MULTIBOOT_CHECKSUM   ; checksum
dd 0x00100000           ; header_addr (where this header is)
dd 0x00100000           ; load_addr (where to load)
dd 0x00200000           ; load_end_addr (end of data to load)
dd 0x00200000           ; bss_end_addr (end of bss)
dd start                ; entry_addr (entry point)

section .bss
align 16
stack_bottom:
resb 16384            ; 16 KiB stack
stack_top:

section .text
global start
extern kernel_main

start:
    ; Set up stack
    mov esp, stack_top

    ; Clear direction flag
    cld

    ; Reset EFLAGS
    push 0
    popf

    ; Call kernel_main (C function)
    call kernel_main

    ; Hang if kernel_main returns
.hang:
    cli               ; Disable interrupts
    hlt               ; Halt processor
    jmp .hang         ; Jump back in case of NMI
