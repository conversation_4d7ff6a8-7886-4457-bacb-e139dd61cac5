; NASM 32-bit multiboot header + start
BITS 32

section .multiboot
align 4
; Multiboot header
dd 0x1BADB002         ; magic
dd 0x00010003         ; flags (align modules + memory info)
dd -(0x1BADB002 + 0x00010003)

section .bss
align 16
stack_bottom:
resb 16384            ; 16 KiB stack
stack_top:

section .text
global start
extern kernel_main

start:
    ; Set up stack
    mov esp, stack_top

    ; Clear direction flag
    cld

    ; Reset EFLAGS
    push 0
    popf

    ; Call kernel_main (C function)
    call kernel_main

    ; Hang if kernel_main returns
.hang:
    cli               ; Disable interrupts
    hlt               ; Halt processor
    jmp .hang         ; Jump back in case of NMI
