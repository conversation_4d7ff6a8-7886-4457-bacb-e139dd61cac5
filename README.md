# KFS-1: Kernel From Scratch - Boot and Screen

A minimal 32-bit x86 kernel that boots via GRUB and displays "42" on the screen.

## Project Overview

This is the first project in the Kernel From Scratch series from 42 School. The goal is to create a basic bootable kernel that:

- Boots via GRUB multiboot
- Has an ASM bootloader with multiboot header
- Contains basic kernel code in C
- Displays "42" on the VGA text screen
- Uses proper compilation flags for freestanding environment

## Requirements

- `gcc` (with 32-bit support)
- `nasm` (Netwide Assembler)
- `ld` (GNU linker)
- `grub-mkrescue` and `xorriso` (for creating bootable ISO)
- `qemu-system-i386` (for testing, optional)

### Installing Dependencies (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install -y gcc nasm grub-mkrescue xorriso qemu-system-x86
```

## Building

```bash
make clean && make -j4
```

This will:
1. Compile the assembly bootloader (`src/boot.asm`)
2. Compile the C kernel (`src/kernel.c`)
3. Link them together using the custom linker script (`linker.ld`)
4. Create a bootable ISO image (`iso/kernel.iso`)

### Makefile Targets

- `make` or `make all` - Build the kernel and ISO
- `make clean` - Clean build artifacts
- `make validate` - Validate the multiboot kernel
- `make test` - Build and test with QEMU (if available)
- `make test-debug` - Build and test with QEMU debug output

## Testing

### Quick Test (Recommended)

```bash
make test
```

### Manual Testing with QEMU

```bash
qemu-system-i386 -cdrom iso/kernel.iso -m 128M
```

For debugging:
```bash
make test-debug
# or manually:
qemu-system-i386 -cdrom iso/kernel.iso -m 128M -serial stdio -d guest_errors
```

### Using VirtualBox or VMware

1. Create a new 32-bit virtual machine
2. Mount `iso/kernel.iso` as a CD-ROM
3. Boot from CD-ROM

## Project Structure

```
kfs-1/
├── src/
│   ├── boot.asm          # Assembly bootloader with multiboot header
│   └── kernel.c          # Main kernel code in C
├── boot/
│   └── grub.cfg          # GRUB configuration
├── iso/                  # Generated ISO directory
├── build/                # Build artifacts
├── Makefile              # Build system
├── linker.ld             # Custom linker script
└── README.md             # This file
```

## Technical Details

### Multiboot Header

The kernel uses the Multiboot specification to be bootable by GRUB. The multiboot header is defined in `src/boot.asm`:

- Magic number: `0x1BADB002`
- Flags: `0x00010003` (align modules + memory info)
- Checksum: Calculated to make the sum zero

### Memory Layout

- Kernel loads at `0x00100000` (1MB)
- VGA text buffer at `0xB8000`
- Stack grows downward from kernel load address

### Compilation Flags

The kernel is compiled with freestanding flags:
- `-m32`: 32-bit target
- `-ffreestanding`: No hosted environment
- `-fno-builtin`: No built-in functions
- `-fno-exceptions`: No C++ exceptions
- `-fno-stack-protector`: No stack protection
- `-nostdlib -nodefaultlibs`: No standard libraries

## Troubleshooting

### "out of memory" or "you need to load the kernel first"

This error can occur due to:

1. **Insufficient VM memory**: Increase VM memory to at least 32MB
2. **GRUB version compatibility**: Try different GRUB versions
3. **Multiboot header issues**: Verify with `grub-file --is-x86-multiboot build/kernel.bin`

### Build Errors

1. **Missing 32-bit support**: Install `gcc-multilib`
2. **Missing GRUB tools**: Install `grub-mkrescue` and `xorriso`
3. **Permission issues**: Ensure build directory is writable

### Testing Script

Use the provided test script for validation:

```bash
./test_kernel.sh
```

## Expected Output

When the kernel boots successfully, you should see:
- GRUB menu with "KFS-1 Kernel (Standard)" and debug options
- After selecting the kernel, the screen should clear and display:
  - "42 - KFS-1 Kernel Loaded Successfully!" in the top-left corner
- The kernel will then halt in an infinite loop with HLT instruction

## Project Status

✅ **COMPLETED FEATURES:**
- ✅ Multiboot-compliant kernel that boots via GRUB
- ✅ Assembly bootloader with proper multiboot header
- ✅ C kernel with VGA text mode output
- ✅ Displays "42" as required by the subject
- ✅ Proper stack setup and memory management
- ✅ Clean screen initialization
- ✅ Freestanding compilation with correct flags
- ✅ Custom linker script for proper memory layout
- ✅ Bootable ISO generation
- ✅ Comprehensive build system with testing targets
- ✅ Full documentation and troubleshooting guide

The project meets all requirements from the KFS-1 subject and is ready for evaluation.

## Next Steps

This kernel provides the foundation for future KFS projects:
- Memory management
- Process management
- System calls
- File systems
- Device drivers

## References

- [OSDev Wiki](https://wiki.osdev.org/)
- [Multiboot Specification](https://www.gnu.org/software/grub/manual/multiboot/multiboot.html)
- [Intel 80386 Reference Manual](https://pdos.csail.mit.edu/6.828/2018/readings/i386/toc.htm)
