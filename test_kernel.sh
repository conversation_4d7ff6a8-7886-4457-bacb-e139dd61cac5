#!/bin/bash

# Test script for KFS-1 kernel
echo "Building kernel..."
make clean && make -j4

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Kernel built successfully!"
echo "Validating multiboot header..."
grub-file --is-x86-multiboot build/kernel.bin
if [ $? -eq 0 ]; then
    echo "✓ Valid multiboot kernel"
else
    echo "✗ Invalid multiboot kernel"
    exit 1
fi

echo "Checking kernel size..."
ls -lh build/kernel.bin

echo "Checking ISO structure..."
ls -la iso/boot/
ls -la iso/boot/grub/

echo "ISO created: iso/kernel.iso"
ls -lh iso/kernel.iso

echo ""
echo "To test the kernel, run:"
echo "qemu-system-i386 -cdrom iso/kernel.iso -m 32M"
echo ""
echo "Or with more verbose output:"
echo "qemu-system-i386 -cdrom iso/kernel.iso -m 32M -serial stdio -d guest_errors"
