# KFS-1 Troubleshooting Guide

## Common Issues and Solutions

### "out of memory" / "you need to load the kernel first" Error

This error typically occurs when GRUB cannot properly load the kernel. Here are the solutions:

#### 1. Virtual Machine Memory Settings

**Problem**: Insufficient memory allocated to the VM
**Solution**: 
- Increase VM memory to at least 64MB (recommended: 128MB)
- For QEMU: `qemu-system-i386 -cdrom iso/kernel.iso -m 128M`
- For VirtualBox: Set memory to 128MB in VM settings
- For VMware: Set memory to 128MB in VM configuration

#### 2. GRUB Version Compatibility

**Problem**: Some GRUB versions have issues with certain multiboot kernels
**Solutions**:
- Try different GRUB boot options
- Use legacy GRUB if available
- Try booting with different flags

#### 3. Kernel Size Issues

**Problem**: Kernel might be too large or have invalid sections
**Verification**:
```bash
# Check kernel size (should be reasonable, < 1MB for this simple kernel)
ls -lh build/kernel.bin

# Verify multiboot header
grub-file --is-x86-multiboot build/kernel.bin

# Check kernel sections
objdump -h build/kernel.bin
```

#### 4. ISO Structure Issues

**Problem**: Incorrect ISO structure or missing files
**Verification**:
```bash
# Check ISO structure
ls -la iso/boot/
ls -la iso/boot/grub/

# Verify GRUB config
cat iso/boot/grub/grub.cfg

# Check if kernel exists and is executable
file iso/boot/kernel.bin
```

#### 5. Alternative Boot Methods

If the ISO doesn't work, try these alternatives:

**Method 1: Direct kernel boot (QEMU)**
```bash
qemu-system-i386 -kernel build/kernel.bin -m 128M
```

**Method 2: Create a simple disk image**
```bash
# Create a floppy image (requires additional setup)
dd if=/dev/zero of=floppy.img bs=1024 count=1440
# ... (additional grub-install steps needed)
```

**Method 3: Use different emulator**
```bash
# Try with different QEMU options
qemu-system-i386 -cdrom iso/kernel.iso -m 128M -machine pc -cpu pentium

# Try with VirtualBox
VBoxManage createvm --name "KFS-1" --register
VBoxManage modifyvm "KFS-1" --memory 128 --boot1 dvd
VBoxManage storagectl "KFS-1" --name "IDE" --add ide
VBoxManage storageattach "KFS-1" --storagectl "IDE" --port 0 --device 0 --type dvddrive --medium iso/kernel.iso
VBoxManage startvm "KFS-1"
```

### Build Issues

#### Missing 32-bit Support
```bash
# Ubuntu/Debian
sudo apt install gcc-multilib

# Fedora/RHEL
sudo dnf install glibc-devel.i686
```

#### Missing GRUB Tools
```bash
# Ubuntu/Debian
sudo apt install grub-mkrescue xorriso

# Fedora/RHEL
sudo dnf install grub2-tools xorriso
```

#### Linker Warnings
The warnings about executable stack and RWX permissions are normal for kernel development and can be ignored.

### Testing Checklist

Before reporting issues, verify:

1. ✅ Kernel builds without errors: `make clean && make`
2. ✅ Multiboot header is valid: `grub-file --is-x86-multiboot build/kernel.bin`
3. ✅ ISO is created: `ls -lh iso/kernel.iso`
4. ✅ VM has sufficient memory (≥128MB)
5. ✅ Using correct architecture (i386/x86)

### Debug Information

To get more debug information:

```bash
# QEMU with debug output
qemu-system-i386 -cdrom iso/kernel.iso -m 128M -serial stdio -d guest_errors,cpu_reset

# Check GRUB debug output
# (Boot from ISO and check GRUB console output)

# Verify kernel loading address
objdump -f build/kernel.bin
```

### Known Working Configurations

These configurations have been tested and work:

1. **QEMU 6.2+ on Ubuntu 22.04**
   ```bash
   qemu-system-i386 -cdrom iso/kernel.iso -m 128M
   ```

2. **VirtualBox 6.1+ with 128MB RAM**
   - Type: Other/Unknown
   - Memory: 128MB
   - Boot order: CD-ROM first

3. **VMware Workstation with 128MB RAM**
   - Guest OS: Other/Unknown
   - Memory: 128MB
   - CD-ROM: iso/kernel.iso

### Getting Help

If you're still experiencing issues:

1. Run the test script: `./test_kernel.sh`
2. Check the exact error message
3. Verify your VM configuration
4. Try the alternative boot methods above
5. Check if your host system supports 32-bit emulation
