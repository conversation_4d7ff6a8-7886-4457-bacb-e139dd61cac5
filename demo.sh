#!/bin/bash

echo "=========================================="
echo "KFS-1: Kernel From Scratch - Demo Script"
echo "=========================================="
echo ""

echo "🔧 Building the kernel..."
make clean && make -j4

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo ""
echo "✅ Build successful!"
echo ""

echo "🔍 Validating the kernel..."
make validate

echo ""
echo "📁 Project structure:"
echo "├── src/"
echo "│   ├── boot.asm          # Assembly bootloader with multiboot header"
echo "│   └── kernel.c          # C kernel code"
echo "├── build/"
echo "│   ├── boot.o            # Compiled bootloader"
echo "│   ├── kernel.o          # Compiled kernel"
echo "│   └── kernel.bin        # Final linked kernel binary"
echo "├── iso/"
echo "│   ├── boot/"
echo "│   │   ├── kernel.bin    # Kernel binary for GRUB"
echo "│   │   └── grub/"
echo "│   │       └── grub.cfg  # GRUB configuration"
echo "│   └── kernel.iso        # Bootable ISO image"
echo "├── Makefile              # Build system"
echo "├── linker.ld             # Custom linker script"
echo "├── README.md             # Documentation"
echo "└── TROUBLESHOOTING.md    # Troubleshooting guide"
echo ""

echo "📊 File sizes:"
ls -lh build/kernel.bin iso/kernel.iso

echo ""
echo "🎯 Project Requirements Status:"
echo "✅ Kernel bootable with GRUB"
echo "✅ ASM boot code with multiboot header"
echo "✅ Basic kernel library with types and functions"
echo "✅ Screen output functionality"
echo "✅ Displays '42' on screen"
echo "✅ Proper compilation flags (-m32, -ffreestanding, etc.)"
echo "✅ Custom linker script"
echo "✅ Makefile with proper rules"
echo "✅ Work under 10 MB (actual: $(du -sh . | cut -f1))"
echo ""

echo "🚀 Ready to test!"
echo ""
echo "To test the kernel:"
echo "  make test                    # Automatic test with QEMU"
echo "  make test-debug              # Test with debug output"
echo ""
echo "Manual testing:"
echo "  qemu-system-i386 -cdrom iso/kernel.iso -m 128M"
echo ""
echo "Expected output:"
echo "  - GRUB menu appears"
echo "  - Select 'KFS-1 Kernel (Standard)'"
echo "  - Screen clears and shows: '42 - KFS-1 Kernel Loaded Successfully!'"
echo "  - Kernel halts (infinite loop)"
echo ""

if command -v qemu-system-i386 >/dev/null 2>&1; then
    echo "🎮 QEMU is available! You can run 'make test' to test immediately."
else
    echo "⚠️  QEMU not found. Install with: sudo apt install qemu-system-x86"
    echo "   Or test with your preferred virtual machine using iso/kernel.iso"
fi

echo ""
echo "📚 For troubleshooting, see TROUBLESHOOTING.md"
echo "📖 For detailed information, see README.md"
echo ""
echo "🎉 KFS-1 project is complete and ready for evaluation!"
