# Minimal Makefile for i386 multiboot kernel
ASM=nasm
CC=gcc
LD=ld
GRUB_MKRESCUE=grub-mkrescue
CFLAGS=-m32 -ffreestanding -fno-builtin -fno-exceptions -fno-stack-protector -nostdlib -nodefaultlibs -Wall -Wextra -g
ASMFLAGS=-f elf32
LDFLAGS=-m elf_i386 -T linker.ld
OBJDIR=build
SRCDIR=src
ISO_DIR=iso
KERNEL_BIN=$(OBJDIR)/kernel.bin
ISO_IMG=$(ISO_DIR)/kernel.iso

all: $(ISO_IMG)

$(OBJDIR):
	mkdir -p $(OBJDIR)

$(OBJDIR)/boot.o: $(SRCDIR)/boot.asm | $(OBJDIR)
	$(ASM) $(ASMFLAGS) -o $@ $<

$(OBJDIR)/kernel.o: $(SRCDIR)/kernel.c | $(OBJDIR)
	$(CC) $(CFLAGS) -c -o $@ $<

$(KERNEL_BIN): $(OBJDIR)/boot.o $(OBJDIR)/kernel.o
	$(LD) $(LDFLAGS) -o $@ $^

$(ISO_DIR)/boot/kernel.bin: $(KERNEL_BIN)
	mkdir -p $(ISO_DIR)/boot/grub
	cp $< $(ISO_DIR)/boot/kernel.bin
	cp boot/grub.cfg $(ISO_DIR)/boot/grub/grub.cfg

$(ISO_IMG): $(ISO_DIR)/boot/kernel.bin
	@# Build ISO using grub-mkrescue; verify dependencies first
	@if ! command -v $(GRUB_MKRESCUE) >/dev/null 2>&1; then \
		echo "Error: grub-mkrescue not found. Please install GRUB tools (grub-mkrescue)"; \
		exit 1; \
	fi
	@if ! command -v xorriso >/dev/null 2>&1; then \
		echo "Error: xorriso not found. Please install xorriso (required by grub-mkrescue)"; \
		exit 1; \
	fi
	$(GRUB_MKRESCUE) -o $@ $(ISO_DIR)

clean:
	rm -rf $(OBJDIR) $(ISO_DIR)/*.iso

test: $(ISO_IMG)
	@echo "Testing kernel with QEMU..."
	@if command -v qemu-system-i386 >/dev/null 2>&1; then \
		qemu-system-i386 -cdrom $(ISO_IMG) -m 128M; \
	else \
		echo "QEMU not found. Install with: sudo apt install qemu-system-x86"; \
		echo "Or test manually by mounting $(ISO_IMG) in your preferred VM"; \
	fi

test-debug: $(ISO_IMG)
	@echo "Testing kernel with QEMU (debug mode)..."
	@if command -v qemu-system-i386 >/dev/null 2>&1; then \
		qemu-system-i386 -cdrom $(ISO_IMG) -m 128M -serial stdio -d guest_errors; \
	else \
		echo "QEMU not found. Install with: sudo apt install qemu-system-x86"; \
	fi

validate: $(KERNEL_BIN)
	@echo "Validating multiboot kernel..."
	@grub-file --is-x86-multiboot $(KERNEL_BIN) && echo "✓ Valid multiboot kernel" || echo "✗ Invalid multiboot kernel"
	@echo "Kernel size: $$(ls -lh $(KERNEL_BIN) | awk '{print $$5}')"
	@echo "ISO size: $$(ls -lh $(ISO_IMG) | awk '{print $$5}')"

.PHONY: all clean test test-debug validate
