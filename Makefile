# Minimal Makefile for i386 multiboot kernel
ASM=nasm
CC=gcc
LD=ld
GRUB_MKRESCUE=grub-mkrescue
CFLAGS=-m32 -ffreestanding -fno-builtin -fno-exceptions -fno-stack-protector -nostdlib -nodefaultlibs -Wall -Wextra -g
ASMFLAGS=-f elf32
LDFLAGS=-m elf_i386 -T linker.ld
OBJDIR=build
SRCDIR=src
ISO_DIR=iso
KERNEL_BIN=$(OBJDIR)/kernel.bin
ISO_IMG=$(ISO_DIR)/kernel.iso

all: $(ISO_IMG)

$(OBJDIR):
	mkdir -p $(OBJDIR)

$(OBJDIR)/boot.o: $(SRCDIR)/boot.asm | $(OBJDIR)
	$(ASM) $(ASMFLAGS) -o $@ $<

$(OBJDIR)/kernel.o: $(SRCDIR)/kernel.c | $(OBJDIR)
	$(CC) $(CFLAGS) -c -o $@ $<

$(KERNEL_BIN): $(OBJDIR)/boot.o $(OBJDIR)/kernel.o
	$(LD) $(LDFLAGS) -o $@ $^

$(ISO_DIR)/boot/kernel.bin: $(KERNEL_BIN)
	mkdir -p $(ISO_DIR)/boot/grub
	cp $< $(ISO_DIR)/boot/kernel.bin
	cp boot/grub.cfg $(ISO_DIR)/boot/grub/grub.cfg

$(ISO_IMG): $(ISO_DIR)/boot/kernel.bin
	# Use grub-mkrescue if available; fallback to xorriso + grub files
	if command -v $(GRUB_MKRESCUE) >/dev/null 2>&1; then \
		$(GRUB_MKRESCUE) -o $@ $(ISO_DIR); \
	else \
		# try xorriso (common on some distributions)
		xorriso -as mkisofs -R -J -l -b boot/grub/i386-pc/eltorito.img -no-emul-boot -boot-load-size 4 -boot-info-table -o $@ $(ISO_DIR); \
	fi

clean:
	rm -rf $(OBJDIR) $(ISO_DIR)/*.iso

.PHONY: all clean
